{
  "$schema": "schema.json",
  "trading_mode": "futures",
  "margin_mode": "isolated",
  "max_open_trades": 5,
  "stake_currency": "USDT",
  "stake_amount": "unlimited",
  "dry_run_wallet": 10000,
  "tradable_balance_ratio": 0.99,
  "fiat_display_currency": "CNY",
  // "leverage_mode": "cross",
  // "leverage": 10,
  "dry_run": true,
  "force_entry_enable": true,
  "initial_state": "running",
  "internals": {
    "process_throttle_secs": 5
  },
  "strategy": "E0V1EN_Optimized",
  "dataformat_trades": "jsongz",
  "cancel_open_orders_on_exit": false,
  "unfilledtimeout": {
    "entry": 10,
    "exit": 30
  },
  "entry_pricing": {
    "price_side": "other",
    "use_order_book": true,
    "order_book_top": 1,
    "price_last_balance": 0.0,
    "check_depth_of_market": {
      "enabled": false,
      "bids_to_ask_delta": 1
    }
  },
  "exit_pricing": {
    "price_side": "other",
    "use_order_book": true,
    "order_book_top": 1
  },
  "exchange": {
    "name": "binance",
    "sandbox": false,
    "key": "",
    "secret": "",
    "enable_ws": true,
    "ccxt_config": {
      // "enableRateLimit": false,
      "httpsProxy": "http://localhost:7899",
      "wsProxy": "http://localhost:7899"
    },
    "ccxt_async_config": {
      // "enableRateLimit": false
    },
    "pair_whitelist": [
      "BTC/USDT:USDT",
      "ETH/USDT:USDT",
      "ETC/USDT:USDT",
      "FIL/USDT:USDT",
      "LTC/USDT:USDT",
      "XRP/USDT:USDT",
      "SOL/USDT:USDT",
      "DOGE/USDT:USDT",
      "ADA/USDT:USDT",
      "TRX/USDT:USDT",
      "TON/USDT:USDT",
      "LINK/USDT:USDT",
      "XLM/USDT:USDT",
      "AVAX/USDT:USDT",
      "SUI/USDT:USDT",
      "HBAR/USDT:USDT",
      "DOT/USDT:USDT",
      "XMR/USDT:USDT",
      "UNI/USDT:USDT",
      "APT/USDT:USDT",
      "NEAR/USDT:USDT",
      "ICP/USDT:USDT",
      "AAVE/USDT:USDT",
      "ATOM/USDT:USDT",
      "VET/USDT:USDT",
      "TAO/USDT:USDT",
      "ENA/USDT:USDT",
      "POL/USDT:USDT",
      "RENDER/USDT:USDT",
      "TIA/USDT:USDT",
      "S/USDT:USDT",
      "ALGO/USDT:USDT",
      "ARB/USDT:USDT",
      "OP/USDT:USDT",
      "MKR/USDT:USDT",
      "EOS/USDT:USDT",
      "FET/USDT:USDT"
    ],
    "pair_blacklist": []
  },
    "pairlists": [
    {
      "method": "StaticPairList"
    },
    {
      "method": "ShuffleFilter",
      "shuffle_frequency": "candle",
      "seed": 42
    }
  ],
  "api_server": {
    "enabled": true,
    "listen_ip_address": "0.0.0.0",
    "listen_port": 18004,
    "verbosity": "error",
    "enable_openapi": false,
    "jwt_secret_key": "a741e55c8f8a4f83b754875586fee12d9478f342c19743ab8724c127bb5346ef",
    "ws_token": "",
    "CORS_origins": ["http://frequi"],
    "username": "cz",
    "password": "juedui"
  }
}
