{"type": "object", "properties": {"max_open_trades": {"description": "最大同时开启的交易数量。-1 表示无限制。", "type": ["integer", "number"], "minimum": -1}, "timeframe": {"description": "使用的时间周期（例如 `1m`、`5m`、`15m`、`30m`、`1h` 等）。\n通常在策略中指定，配置中可能缺失。", "type": "string"}, "proxy_coin": {"description": "代理币种 - 必须用于特定的期货模式（例如 BNFCR）。", "type": "string"}, "stake_currency": {"description": "用于质押的货币。", "type": "string"}, "stake_amount": {"description": "每笔交易的质押金额。", "type": ["number", "string"], "minimum": 0.0001, "pattern": "unlimited"}, "tradable_balance_ratio": {"description": "可交易余额的比例。", "type": "number", "minimum": 0.0, "maximum": 1, "default": 0.99}, "available_capital": {"description": "可用于交易的总资本。", "type": "number", "minimum": 0}, "amend_last_stake_amount": {"description": "是否调整最后的质押金额。", "type": "boolean", "default": false}, "last_stake_amount_min_ratio": {"description": "最后质押金额的最小比例。", "type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.5}, "fiat_display_currency": {"description": "用于显示的法定货币。", "type": "string", "enum": ["AUD", "BRL", "CAD", "CHF", "CLP", "CNY", "CZK", "DKK", "EUR", "GBP", "HKD", "HUF", "IDR", "ILS", "INR", "JPY", "KRW", "MXN", "MYR", "NOK", "NZD", "PHP", "PKR", "PLN", "RUB", "UAH", "SEK", "SGD", "THB", "TRY", "TWD", "ZAR", "USD", "BTC", "ETH", "XRP", "LTC", "BCH", "BNB", ""]}, "dry_run": {"description": "启用或禁用模拟运行模式。", "type": "boolean"}, "dry_run_wallet": {"description": "模拟运行模式的初始钱包余额。", "type": ["number", "object"], "default": 1000, "patternProperties": {"^[a-zA-Z0-9]+$": {"type": "number"}}, "additionalProperties": false}, "cancel_open_orders_on_exit": {"description": "退出时取消未完成的订单。", "type": "boolean", "default": false}, "process_only_new_candles": {"description": "仅处理新的 K 线数据。", "type": "boolean"}, "minimal_roi": {"description": "最低投资回报率。\n通常在策略中指定，配置中可能缺失。", "type": "object", "patternProperties": {"^[0-9.]+$": {"type": "number"}}}, "amount_reserve_percent": {"description": "保留金额的百分比。", "type": "number", "minimum": 0.0, "maximum": 0.5}, "stoploss": {"description": "用于止损的值（以比例表示）。\n通常在策略中指定，配置中可能缺失。", "type": "number", "maximum": 0, "exclusiveMaximum": true}, "trailing_stop": {"description": "启用或禁用追踪止损。\n通常在策略中指定，配置中可能缺失。", "type": "boolean"}, "trailing_stop_positive": {"description": "追踪止损的正偏移量。\n通常在策略中指定，配置中可能缺失。", "type": "number", "minimum": 0, "maximum": 1}, "trailing_stop_positive_offset": {"description": "激活追踪止损的偏移量。\n通常在策略中指定，配置中可能缺失。", "type": "number", "minimum": 0, "maximum": 1}, "trailing_only_offset_is_reached": {"description": "仅在达到偏移量时使用追踪止损。\n通常在策略中指定，配置中可能缺失。", "type": "boolean"}, "use_exit_signal": {"description": "使用退出信号进行交易。\n通常在策略中指定，配置中可能缺失。", "type": "boolean"}, "exit_profit_only": {"description": "仅在盈利时退出。当利润低于 `exit_profit_offset` 时忽略退出信号。\n通常在策略中指定，配置中可能缺失。", "type": "boolean"}, "exit_profit_offset": {"description": "利润退出的偏移量。\n通常在策略中指定，配置中可能缺失。", "type": "number"}, "fee": {"description": "交易费用百分比。可用于在回测中模拟滑点。", "type": "number", "minimum": 0, "maximum": 0.1}, "ignore_roi_if_entry_signal": {"description": "如果存在进入信号，则忽略 ROI。\n通常在策略中指定，配置中可能缺失。", "type": "boolean"}, "ignore_buying_expired_candle_after": {"description": "在蜡烛过期时间后忽略买入。\n通常在策略中指定，配置中可能缺失。", "type": "number"}, "trading_mode": {"description": "交易模式（例如现货、杠杆）。", "type": "string", "enum": ["spot", "margin", "futures"]}, "margin_mode": {"description": "杠杆交易模式。", "type": "string", "enum": ["cross", "isolated", ""]}, "reduce_df_footprint": {"description": "通过将列转换为 float32/int32 来减少 DataFrame 的占用空间。", "type": "boolean", "default": false}, "minimum_trade_amount": {"description": "交易的最小金额 - 仅用于前瞻性分析。", "type": "number", "default": 10}, "targeted_trade_amount": {"description": "前瞻性分析的目标交易金额。", "type": "number", "default": 20}, "lookahead_analysis_exportfilename": {"description": "前瞻性分析导出的 CSV 文件名。", "type": "string"}, "startup_candle": {"description": "启动蜡烛配置。", "type": "array", "uniqueItems": true, "default": [199, 399, 499, 999, 1999]}, "liquidation_buffer": {"description": "清算缓冲比例。", "type": "number", "minimum": 0.0, "maximum": 0.99}, "backtest_breakdown": {"description": "回测的分解配置。", "type": "array", "items": {"type": "string", "enum": ["day", "week", "month", "year"]}}, "bot_name": {"description": "交易机器人的名称。通过 API 传递给客户端。", "type": "string"}, "unfilledtimeout": {"description": "未完成订单的超时配置。\n通常在策略中指定，配置中可能缺失。", "type": "object", "properties": {"entry": {"description": "进入订单的超时时间（单位）。", "type": "number", "minimum": 1}, "exit": {"description": "退出订单的超时时间（单位）。", "type": "number", "minimum": 1}, "exit_timeout_count": {"description": "在放弃之前重试退出订单的次数。", "type": "number", "minimum": 0, "default": 0}, "unit": {"description": "超时时间的单位（例如秒、分钟）。", "type": "string", "enum": ["minutes", "seconds"], "default": "minutes"}}}, "entry_pricing": {"description": "入场定价的配置。", "type": "object", "properties": {"price_last_balance": {"description": "最后价格的余额比例。", "type": "number", "minimum": 0, "maximum": 1, "exclusiveMaximum": false}, "price_side": {"description": "使用价格的方向（例如，买入价、卖出价、相同）。", "type": "string", "enum": ["ask", "bid", "same", "other"], "default": "same"}, "use_order_book": {"description": "是否使用订单簿进行定价。", "type": "boolean"}, "order_book_top": {"description": "考虑订单簿的前 N 个级别。", "type": "integer", "minimum": 1, "maximum": 50}, "check_depth_of_market": {"description": "检查市场深度的配置。", "type": "object", "properties": {"enabled": {"description": "启用或禁用市场深度检查。", "type": "boolean"}, "bids_to_ask_delta": {"description": "买入价与卖出价之间的差值。", "type": "number", "minimum": 0}}}}, "required": ["price_side"]}, "exit_pricing": {"description": "退出定价的配置。", "type": "object", "properties": {"price_side": {"description": "使用价格的方向（例如，买入价、卖出价、相同）。", "type": "string", "enum": ["ask", "bid", "same", "other"], "default": "same"}, "price_last_balance": {"description": "最后价格的余额比例。", "type": "number", "minimum": 0, "maximum": 1, "exclusiveMaximum": false}, "use_order_book": {"description": "是否使用订单簿进行定价。", "type": "boolean"}, "order_book_top": {"description": "考虑订单簿的前 N 个级别。", "type": "integer", "minimum": 1, "maximum": 50}}, "required": ["price_side"]}, "custom_price_max_distance_ratio": {"description": "当前价格与自定义进入或退出价格之间的最大距离比例。", "type": "number", "minimum": 0.0, "maximum": 1, "default": 0.02}, "order_types": {"description": "订单类型的配置。\n通常在策略中指定，配置中可能缺失。", "type": "object", "properties": {"entry": {"description": "进入订单的类型（例如，限价单、市价单）。", "type": "string", "enum": ["limit", "market"]}, "exit": {"description": "退出订单的类型（例如，限价单、市价单）。", "type": "string", "enum": ["limit", "market"]}, "force_exit": {"description": "强制退出订单的类型（例如，限价单、市价单）。", "type": "string", "enum": ["limit", "market"]}, "force_entry": {"description": "强制进入订单的类型（例如，限价单、市价单）。", "type": "string", "enum": ["limit", "market"]}, "emergency_exit": {"description": "紧急退出订单的类型（例如，限价单、市价单）。", "type": "string", "enum": ["limit", "market"], "default": "market"}, "stoploss": {"description": "止损订单的类型（例如，限价单、市价单）。", "type": "string", "enum": ["limit", "market"]}, "stoploss_on_exchange": {"description": "是否在交易所设置止损。", "type": "boolean"}, "stoploss_price_type": {"description": "止损的价格类型（例如，最后价格、标记价格、指数价格）。", "type": "string", "enum": ["last", "mark", "index"]}, "stoploss_on_exchange_interval": {"description": "在交易所设置止损的时间间隔（秒）。", "type": "number"}, "stoploss_on_exchange_limit_ratio": {"description": "在交易所设置止损的限制比例。", "type": "number", "minimum": 0.0, "maximum": 1.0}}, "required": ["entry", "exit", "stoploss", "stoploss_on_exchange"]}, "order_time_in_force": {"description": "订单的时效性配置。\n通常在策略中指定，配置中可能缺失。", "type": "object", "properties": {"entry": {"description": "进入订单的时效性。", "type": "string", "enum": ["GTC", "FOK", "IOC", "PO", "gtc", "fok", "ioc", "po"]}, "exit": {"description": "退出订单的时效性。", "type": "string", "enum": ["GTC", "FOK", "IOC", "PO", "gtc", "fok", "ioc", "po"]}}, "required": ["entry", "exit"]}, "coingecko": {"description": "CoinGecko API 的配置。", "type": "object", "properties": {"is_demo": {"description": "是否以演示模式使用 CoinGecko。", "type": "boolean", "default": true}, "api_key": {"description": "访问 CoinGecko 的 API 密钥。", "type": "string"}}, "required": ["is_demo", "api_key"]}, "exchange": {"description": "交易所配置。", "$ref": "#/definitions/exchange"}, "edge": {"description": "Edge 配置。", "$ref": "#/definitions/edge"}, "log_config": {"description": "日志配置。", "$ref": "#/definitions/logging"}, "freqai": {"description": "FreqAI 配置。", "$ref": "#/definitions/freqai"}, "external_message_consumer": {"description": "外部消息消费者的配置。", "$ref": "#/definitions/external_message_consumer"}, "experimental": {"description": "实验性配置。", "type": "object", "properties": {"block_bad_exchanges": {"type": "boolean"}}}, "pairlists": {"description": "交易对列表的配置。", "type": "array", "items": {"type": "object", "properties": {"method": {"description": "用于生成交易对列表的方法。", "type": "string", "enum": ["StaticPairList", "VolumePairList", "PercentChangePairList", "ProducerPairList", "RemotePairList", "MarketCapPairList", "<PERSON><PERSON><PERSON>er", "FullTradesFilter", "OffsetFilter", "PerformanceFilter", "PrecisionFilter", "PriceFilter", "RangeStabilityFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sp<PERSON><PERSON><PERSON>er", "VolatilityFilter"]}}, "required": ["method"]}}, "telegram": {"description": "Telegram 设置。", "type": "object", "properties": {"enabled": {"description": "启用 Telegram 通知。", "type": "boolean"}, "token": {"description": "Telegram 机器人令牌。", "type": "string"}, "chat_id": {"description": "Telegram 聊天或群组 ID。", "type": "string"}, "topic_id": {"description": "Telegram 主题 ID - 仅适用于群组聊天。", "type": "string"}, "authorized_users": {"description": "机器人授权用户。", "type": "array", "items": {"type": "string"}, "uniqueItems": true}, "allow_custom_messages": {"description": "允许从策略发送自定义消息。", "type": "boolean", "default": true}, "balance_dust_level": {"description": "最低余额水平以视为尘埃。", "type": "number", "minimum": 0.0}, "notification_settings": {"description": "不同类型通知的设置。", "type": "object", "default": {}, "properties": {"status": {"description": "状态更新的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"]}, "warning": {"description": "警告的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"]}, "startup": {"description": "启动消息的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"]}, "entry": {"description": "进入信号的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"]}, "entry_fill": {"description": "进入成交信号的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"], "default": "off"}, "entry_cancel": {"description": "进入取消信号的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"]}, "exit": {"description": "退出信号的 Telegram 设置。", "type": ["string", "object"], "additionalProperties": {"type": "string", "enum": ["on", "off", "silent"]}}, "exit_fill": {"description": "退出成交信号的 Telegram 设置。", "type": ["string", "object"], "additionalProperties": {"type": "string", "enum": ["on", "off", "silent"]}, "default": "on"}, "exit_cancel": {"description": "退出取消信号的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"]}, "protection_trigger": {"description": "保护触发器的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"], "default": "on"}, "protection_trigger_global": {"description": "全局保护触发器的 Telegram 设置。", "type": "string", "enum": ["on", "off", "silent"], "default": "on"}}}, "reload": {"description": "为某些消息添加重新加载按钮。", "type": "boolean"}}, "required": ["enabled", "token", "chat_id"]}, "webhook": {"description": "Webhook 设置。", "type": "object", "properties": {"enabled": {"type": "boolean"}, "url": {"type": "string"}, "format": {"type": "string", "enum": ["form", "json", "raw"], "default": "form"}, "retries": {"type": "integer", "minimum": 0}, "retry_delay": {"type": "number", "minimum": 0}, "status": {"type": "object"}, "warning": {"type": "object"}, "exception": {"type": "object"}, "startup": {"type": "object"}, "entry": {"type": "object"}, "entry_fill": {"type": "object"}, "entry_cancel": {"type": "object"}, "exit": {"type": "object"}, "exit_fill": {"type": "object"}, "exit_cancel": {"type": "object"}, "protection_trigger": {"type": "object"}, "protection_trigger_global": {"type": "object"}, "strategy_msg": {"type": "object"}, "whitelist": {"type": "object"}, "analyzed_df": {"type": "object"}, "new_candle": {"type": "object"}}}, "discord": {"description": "Discord 设置。", "type": "object", "properties": {"enabled": {"type": "boolean"}, "webhook_url": {"type": "string"}, "exit_fill": {"type": "array", "items": {"type": "object"}, "default": [{"Trade ID": "{trade_id}"}, {"Exchange": "{exchange}"}, {"Pair": "{pair}"}, {"Direction": "{direction}"}, {"Open rate": "{open_rate}"}, {"Close rate": "{close_rate}"}, {"Amount": "{amount}"}, {"Open date": "{open_date:%Y-%m-%d %H:%M:%S}"}, {"Close date": "{close_date:%Y-%m-%d %H:%M:%S}"}, {"Profit": "{profit_amount} {stake_currency}"}, {"Profitability": "{profit_ratio:.2%}"}, {"Enter tag": "{enter_tag}"}, {"Exit Reason": "{exit_reason}"}, {"Strategy": "{strategy}"}, {"Timeframe": "{timeframe}"}]}, "entry_fill": {"type": "array", "items": {"type": "object"}, "default": [{"Trade ID": "{trade_id}"}, {"Exchange": "{exchange}"}, {"Pair": "{pair}"}, {"Direction": "{direction}"}, {"Open rate": "{open_rate}"}, {"Amount": "{amount}"}, {"Open date": "{open_date:%Y-%m-%d %H:%M:%S}"}, {"Enter tag": "{enter_tag}"}, {"Strategy": "{strategy} {timeframe}"}]}}}, "api_server": {"description": "API 服务器设置。", "type": "object", "properties": {"enabled": {"description": "是否启用 API 服务器。", "type": "boolean"}, "listen_ip_address": {"description": "API 服务器监听的 IP 地址。", "format": "ipv4"}, "listen_port": {"description": "API 服务器监听的端口。", "type": "integer", "minimum": 1024, "maximum": 65535}, "username": {"description": "API 服务器认证的用户名。", "type": "string"}, "password": {"description": "API 服务器认证的密码。", "type": "string"}, "ws_token": {"description": "API 服务器的 WebSocket 令牌。", "type": ["string", "array"], "items": {"type": "string"}}, "jwt_secret_key": {"description": "JWT 认证的密钥。", "type": "string"}, "CORS_origins": {"description": "允许的 CORS 来源列表。", "type": "array", "items": {"type": "string"}}, "verbosity": {"description": "日志详细级别。", "type": "string", "enum": ["error", "info"]}}, "required": ["enabled", "listen_ip_address", "listen_port", "username", "password"]}, "db_url": {"description": "数据库连接 URL。", "type": "string"}, "export": {"description": "要导出的数据类型。", "type": "string", "enum": ["none", "trades", "signals"], "default": "trades"}, "disableparamexport": {"description": "禁用参数导出。", "type": "boolean"}, "initial_state": {"description": "系统的初始状态。", "type": "string", "enum": ["running", "paused", "stopped"]}, "force_entry_enable": {"description": "强制启用进入。", "type": "boolean"}, "disable_dataframe_checks": {"description": "禁用对数据框的检查。", "type": "boolean"}, "internals": {"description": "内部设置。", "type": "object", "default": {}, "properties": {"process_throttle_secs": {"description": "单次机器人迭代的最小循环持续时间（秒）。", "type": "integer"}, "interval": {"description": "间隔时间（秒）。", "type": "integer"}, "sd_notify": {"description": "启用 systemd 通知。", "type": "boolean"}}}, "dataformat_ohlcv": {"description": "OHLCV 数据的格式。", "type": "string", "enum": ["json", "jsongz", "feather", "parquet"], "default": "feather"}, "dataformat_trades": {"description": "交易数据的格式。", "type": "string", "enum": ["json", "jsongz", "feather", "parquet"], "default": "feather"}, "position_adjustment_enable": {"description": "启用仓位调整。\n通常在策略中指定，配置中可能缺失。", "type": "boolean"}, "new_pairs_days": {"description": "下载新交易对的指定天数数据。", "type": "integer", "default": 30}, "download_trades": {"description": "默认下载交易数据（而不是 OHLCV 数据）。", "type": "boolean"}, "max_entry_position_adjustment": {"description": "允许的最大进入仓位调整。\n通常在策略中指定，配置中可能缺失。", "type": ["integer", "number"], "minimum": -1}, "add_config_files": {"description": "要加载的其他配置文件。", "type": "array", "items": {"type": "string"}}, "orderflow": {"description": "与订单流相关的设置。", "type": "object", "properties": {"cache_size": {"description": "订单流数据的缓存大小。", "type": "number", "minimum": 1, "default": 1500}, "max_candles": {"description": "要考虑的最大蜡烛数量。", "type": "number", "minimum": 1, "default": 1500}, "scale": {"description": "订单流数据的缩放因子。", "type": "number", "minimum": 0.0}, "stacked_imbalance_range": {"description": "堆叠不平衡的范围。", "type": "number", "minimum": 0}, "imbalance_volume": {"description": "不平衡的体积阈值。", "type": "number", "minimum": 0}, "imbalance_ratio": {"description": "不平衡的比例阈值。", "type": "number", "minimum": 0.0}}, "required": ["max_candles", "scale", "stacked_imbalance_range", "imbalance_volume", "imbalance_ratio"]}}, "definitions": {"exchange": {"description": "交易所配置设置。", "type": "object", "properties": {"name": {"description": "交易所的名称。", "type": "string"}, "enable_ws": {"description": "启用与交易所的 WebSocket 连接。", "type": "boolean", "default": true}, "key": {"description": "交易所的 API 密钥。", "type": "string", "default": ""}, "secret": {"description": "交易所的 API 密钥。", "type": "string", "default": ""}, "password": {"description": "交易所的密码（如果需要）。", "type": "string", "default": ""}, "uid": {"description": "交易所的用户 ID（如果需要）。", "type": "string"}, "pair_whitelist": {"description": "白名单中的交易对列表。", "type": "array", "items": {"type": "string"}, "uniqueItems": true}, "pair_blacklist": {"description": "黑名单中的交易对列表。", "type": "array", "items": {"type": "string"}, "uniqueItems": true}, "log_responses": {"description": "记录来自交易所的响应。用于/需要调试订单处理问题。", "type": "boolean", "default": false}, "unknown_fee_rate": {"description": "未知市场的费用率。", "type": "number"}, "outdated_offset": {"description": "过期数据的偏移量（分钟）。", "type": "integer", "minimum": 1}, "markets_refresh_interval": {"description": "刷新市场数据的时间间隔（分钟）。", "type": "integer", "default": 60}, "ccxt_config": {"description": "CCXT 配置设置。", "type": "object"}, "ccxt_async_config": {"description": "CCXT 异步配置设置。", "type": "object"}}, "required": ["name"]}, "edge": {"type": "object", "properties": {"enabled": {"description": "是否启用 Edge。", "type": "boolean"}, "process_throttle_secs": {"description": "处理的最小间隔时间（秒）。", "type": "integer", "minimum": 600}, "calculate_since_number_of_days": {"description": "计算的天数范围。", "type": "integer"}, "allowed_risk": {"description": "允许的风险值。", "type": "number"}, "stoploss_range_min": {"description": "止损范围的最小值。", "type": "number"}, "stoploss_range_max": {"description": "止损范围的最大值。", "type": "number"}, "stoploss_range_step": {"description": "止损范围的步长。", "type": "number"}, "minimum_winrate": {"description": "最低胜率。", "type": "number"}, "minimum_expectancy": {"description": "最低期望值。", "type": "number"}, "min_trade_number": {"description": "最小交易数量。", "type": "number"}, "max_trade_duration_minute": {"description": "最大交易持续时间（分钟）。", "type": "integer"}, "remove_pumps": {"description": "是否移除价格波动异常。", "type": "boolean"}}, "required": ["process_throttle_secs", "allowed_risk"]}, "logging": {"type": "object", "properties": {"version": {"description": "日志配置的版本。", "type": "number", "const": 1}, "formatters": {"description": "日志格式化器配置。", "type": "object"}, "handlers": {"description": "日志处理器配置。", "type": "object"}, "root": {"description": "根日志记录器配置。", "type": "object"}}, "required": ["version", "formatters", "handlers", "root"]}, "external_message_consumer": {"description": "外部消息消费者的配置。", "type": "object", "properties": {"enabled": {"description": "是否启用外部消息消费者。", "type": "boolean", "default": false}, "producers": {"description": "外部消息消费者的生产者列表。", "type": "array", "items": {"type": "object", "properties": {"name": {"description": "生产者的名称。", "type": "string"}, "host": {"description": "生产者的主机地址。", "type": "string"}, "port": {"description": "生产者的端口。", "type": "integer", "default": 8080, "minimum": 0, "maximum": 65535}, "secure": {"description": "是否使用 SSL 连接到生产者。", "type": "boolean", "default": false}, "ws_token": {"description": "生产者的 WebSocket 令牌。", "type": "string"}}, "required": ["name", "host", "ws_token"]}}, "wait_timeout": {"description": "等待超时时间（秒）。", "type": "integer", "minimum": 0}, "sleep_time": {"description": "重试连接前的休眠时间（秒）。", "type": "integer", "minimum": 0}, "ping_timeout": {"description": "Ping 超时时间（秒）。", "type": "integer", "minimum": 0}, "remove_entry_exit_signals": {"description": "从数据框中移除信号列（将其设置为 0）。", "type": "boolean", "default": false}, "initial_candle_limit": {"description": "初始蜡烛限制。", "type": "integer", "minimum": 0, "maximum": 1500, "default": 1500}, "message_size_limit": {"description": "消息大小限制（以 MB 为单位）。", "type": "integer", "minimum": 1, "maximum": 20, "default": 8}}, "required": ["producers"]}, "freqai": {"type": "object", "properties": {"enabled": {"description": "是否启用 freqAI。", "type": "boolean", "default": false}, "identifier": {"description": "当前模型的唯一 ID。修改特性时必须更改。", "type": "string", "default": "example"}, "write_metrics_to_disk": {"description": "是否将指标写入磁盘？", "type": "boolean", "default": false}, "purge_old_models": {"description": "磁盘上保留的模型数量。", "type": ["boolean", "number"], "default": 2}, "conv_width": {"description": "神经网络输入张量的宽度。", "type": "integer", "default": 1}, "train_period_days": {"description": "用于训练数据的天数（滑动窗口的宽度）。", "type": "integer", "default": 0}, "backtest_period_days": {"description": "从训练模型推断的天数，然后滑动 `train_period_days` 窗口。", "type": "number", "default": 7}, "live_retrain_hours": {"description": "干/实时运行期间的重新训练频率（小时）。", "type": "number", "default": 0}, "expiration_hours": {"description": "如果模型超过 `expiration_hours`，则避免进行预测。默认为 0（无过期）。", "type": "number", "default": 0}, "save_backtest_models": {"description": "在回测时将模型保存到磁盘。", "type": "boolean", "default": false}, "fit_live_predictions_candles": {"description": "用于从预测数据计算目标（标签）统计的历史蜡烛数量，而不是从训练数据集中计算。", "type": "integer"}, "data_kitchen_thread_count": {"description": "指定用于数据处理（异常值方法、归一化等）的线程数量。", "type": "integer"}, "activate_tensorboard": {"description": "是否激活 TensorBoard。", "type": "boolean", "default": true}, "wait_for_training_iteration_on_reload": {"description": "在 /reload 或 ctrl+c 后等待下一次训练迭代完成。", "type": "boolean", "default": true}, "continual_learning": {"description": "使用最近训练模型的最终状态作为新模型的起点，允许增量学习。", "type": "boolean", "default": false}, "keras": {"description": "使用 Keras 进行模型训练。", "type": "boolean", "default": false}, "feature_parameters": {"description": "用于工程特性集的参数。", "type": "object", "properties": {"include_corr_pairlist": {"description": "要包含在特性中的相关交易对列表。", "type": "array"}, "include_timeframes": {"description": "所有 `feature_engineering_expand_*()` 中指标将创建的时间周期列表。", "type": "array"}, "label_period_candles": {"description": "用于标记周期的未来蜡烛数量。这可以在 `set_freqai_targets()` 中使用。", "type": "integer"}, "include_shifted_candles": {"description": "将前一蜡烛的特性添加到后续蜡烛中，以添加历史信息。", "type": "integer", "default": 0}, "DI_threshold": {"description": "当设置为 > 0 时，激活使用相异性指数进行异常值检测。", "type": "number", "default": 0}, "weight_factor": {"description": "根据数据点的新近性加权训练数据点。", "type": "number", "default": 0}, "principal_component_analysis": {"description": "使用主成分分析自动减少数据集的维度。", "type": "boolean", "default": false}, "use_SVM_to_remove_outliers": {"description": "使用 SVM 从特性中移除异常值。", "type": "boolean", "default": false}, "plot_feature_importances": {"description": "为每个模型创建特性重要性图。", "type": "integer", "default": 0}, "svm_params": {"description": "Sklearn 的 `SGDOneClassSVM()` 中可用的所有参数。", "type": "object", "properties": {"shuffle": {"description": "在应用 SVM 之前是否打乱数据。", "type": "boolean", "default": false}, "nu": {"description": "SVM 的 nu 参数。", "type": "number", "default": 0.1}}}, "shuffle_after_split": {"description": "将数据分为训练集和测试集，然后分别打乱两组。", "type": "boolean", "default": false}, "buffer_train_data_candles": {"description": "在指标填充后，从训练数据的开头和结尾剪切 `buffer_train_data_candles`。", "type": "integer", "default": 0}}, "required": ["include_timeframes", "include_corr_pairlist"]}, "data_split_parameters": {"descriptions": "scikit-learn 的 test_train_split() 函数的附加参数。", "type": "object", "properties": {"test_size": {"description": "测试集的大小。", "type": "number"}, "random_state": {"description": "随机种子。", "type": "integer"}, "shuffle": {"description": "是否打乱数据。", "type": "boolean", "default": false}}}, "model_training_parameters": {"description": "包含所选模型库可用的所有参数的灵活字典。", "type": "object"}, "rl_config": {"type": "object", "properties": {"drop_ohlc_from_features": {"description": "不在特性集中包含归一化的 OHLC 数据。", "type": "boolean", "default": false}, "train_cycles": {"description": "要执行的训练周期数。", "type": "integer"}, "max_trade_duration_candles": {"description": "指导代理训练以保持交易低于期望长度。", "type": "integer"}, "add_state_info": {"description": "在特性集中包含状态信息以进行训练和推断。", "type": "boolean", "default": false}, "max_training_drawdown_pct": {"description": "训练期间允许的最大回撤百分比。", "type": "number", "default": 0.02}, "cpu_count": {"description": "用于训练的线程/CPU 数量。", "type": "integer", "default": 1}, "model_type": {"description": "来自 stable_baselines3 或 SBcontrib 的模型字符串。", "type": "string", "default": "PPO"}, "policy_type": {"description": "stable_baselines3 中可用的策略类型之一。", "type": "string", "default": "MlpPolicy"}, "net_arch": {"description": "神经网络的架构。", "type": "array", "default": [128, 128]}, "randomize_starting_position": {"description": "随机化每个回合的起始点以避免过拟合。", "type": "boolean", "default": false}, "progress_bar": {"description": "显示当前进度的进度条。", "type": "boolean", "default": true}, "model_reward_parameters": {"description": "配置奖励模型的参数。", "type": "object", "properties": {"rr": {"description": "奖励比率参数。", "type": "number", "default": 1}, "profit_aim": {"description": "利润目标参数。", "type": "number", "default": 0.025}}}}}}, "required": ["enabled", "train_period_days", "backtest_period_days", "identifier", "feature_parameters", "data_split_parameters"]}}}